const fs = require('fs');
const path = require('path');
const { runAutonetScraper } = require('./scrapers/autonet/index');
const { runAutototalScraper, runAutototalScraperModular } = require('./scrapers/autototal/index');
const { runIntercarsScraper } = require('./scrapers/intercars/index');
const { runAutopartnerScraper } = require('./scrapers/autopartner/index');
const { runMateromScraper } = require('./scrapers/materom/index');
const { runAjsScraper } = require('./scrapers/ajs/index');
const { runElitScraper } = require('./scrapers/elit/index');
const { runBardiScraper } = require('./scrapers/bardi/index');
const { SUPP } = require ("./shared/suppliers");
// --- Configuration ---
// In a real application, you might get this from command-line arguments,
// an API request, or a configuration file.
//const scraperToRun = 'AUTOTOTAL'; // Example: Select the scraper
//const scraperToRun = 'INTERCARS'; // Example: Select the scraper
//const scraperToRun = 'AUTONET'; // Example: Select the scraper
const scraperToRun = 'AUTOPARTNER'; // Example: Select the scraper
//const scraperToRun = 'MATEROM'; // Example: Select the scraper
//const scraperToRun = 'AJS'; // Example: Select the scraper
//const scraperToRun = 'ELIT'; // Example: Select the scraper
//const scraperToRun = 'BARDI'; // Example: Select the scraper
//const productCodeToSearch = 'GDB1550'; // Example: Product code
//const productCodeToSearch = '6464380002'; // Example: Product code
const productCodeToSearch = '1k1423055mx'; // Example: Product code
// --- End Configuration ---

async function main() {
  console.log(`--- Starting Scraper Execution ---`);
  let results;

  try {
    switch (scraperToRun) { // Removed .toLowerCase() for direct comparison with SUPP constants
      case SUPP.AUTONET:
        results = await runAutonetScraper(productCodeToSearch);
        break;

      case SUPP.AUTOTOTAL:
        results = await runAutototalScraperModular(productCodeToSearch);
        break;

      case SUPP.AUTOPARTNER:
        results = await runAutopartnerScraper(productCodeToSearch);
        break;

      case SUPP.INTERCARS:
        results = await runIntercarsScraper(productCodeToSearch);
        break;

      case SUPP.MATEROM:
        results = await runMateromScraper(productCodeToSearch);
        break;

      case SUPP.AJS:
        results = await runAjsScraper(productCodeToSearch);
        break;

      case SUPP.ELIT:
        results = await runElitScraper(productCodeToSearch);
        break;

      case SUPP.BARDI:
        results = await runBardiScraper(productCodeToSearch);
        break;

      default:
        console.error(`❌ Unknown scraper specified: ${scraperToRun}`);
        process.exit(1); // Exit with error code
    }

    if (results) {
      console.log(`📊 Scraped ${results.length} products.`);
      console.log(`\n--- Scraper ${scraperToRun} finished successfully ---`);

      // Log unique delivery values as a set
      if (Array.isArray(results) && results.length > 0) {
        // console.log('\n--- Unique Delivery Values Set ---');

        // // Create a set of unique delivery values
        // const deliverySet = new Set();

        // results.forEach(result => {
        //   if (result.delivery !== undefined) {
        //     deliverySet.add(typeof result.delivery === 'object'
        //       ? JSON.stringify(result.delivery)
        //       : result.delivery);
        //   }
        // });

        // // Convert set to array and display
        // const uniqueDeliveryValues = Array.from(deliverySet);
        // console.log(uniqueDeliveryValues);

        // console.log(`Total unique delivery values: ${uniqueDeliveryValues.length}`);
        // console.log('--- End of Unique Delivery Values ---\n');

        //Log the full results JSON
        // console.log('\n--- Results JSON ---');
        // console.log(JSON.stringify(results, null, 2));
        // console.log('--- End of Results JSON ---\n');

        // Save results to index-results.json in the root directory
        try {
          const outputPath = path.resolve(__dirname, '../index-results.json');
          const outputData = {
            scraper: scraperToRun,
            productCode: productCodeToSearch,
            timestamp: new Date().toISOString(),
            totalProducts: results.length,
            results: results
          };

          fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2), 'utf8');
          console.log(`💾 Results saved to: ${outputPath}`);
        } catch (saveError) {
          console.error('❌ Error saving results to file:', saveError);
        }
      }

    } else {
      console.error(`\n--- Scraper ${scraperToRun} failed or returned no results ---`);
    }
  } catch (error) {
    console.error(`\n--- An unexpected error occurred in main execution ---`);
    console.error(error);
    process.exit(1);
  }
}

// Execute the main function
main();