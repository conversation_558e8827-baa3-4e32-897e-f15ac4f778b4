const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');

const { connect } = require('puppeteer-real-browser');


const SCRAPER_NAME = 'INTERCARS';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile');

const LOGIN_URL = 'https://account.intercars.eu/authenticationendpoint/login.do';
const DEFAULT_TEST_PAGE = 'https://ro.e-cat.intercars.eu/ro/';

const LOGGED_IN_SELECTOR = '.headermenu__trigger.js-menu-trigger';
const USERNAME_SELECTOR = 'input#usernameUserInput';
const NEXT_BUTTON_SELECTOR = 'input[data-testid="identifier-auth-continue-button"]';
const PASSWORD_SELECTOR = 'input[data-testid="login-page-password-input"]';
const KEEP_ME_AUTHENTICATED_SELECTOR = 'input#chkRemember';
const SIGN_IN_BUTTON_SELECTOR = 'button#sign-in-button';
const COOKIE_ACCEPT_BUTTON_SELECTOR = 'button[data-testid="cookie-consent-banner-confirm-button"]';

const SEARCH_INPUT_SELECTOR = 'input[data-testid="query-input"]';
const SEARCH_BUTTON_SELECTOR = 'button[data-testid="header-search-button-submit"]';
const PRICE_SELECTOR = '.price_final > span';
const RESULTS_TABLE_SELECTOR = '.art_wrapper';
const PRICE_REQUEST_BUTTON_SELECTOR = 'button.btn-lightx.bold';

/**
 * Main function to run the Intercars scraper.
 * @param {string} productCode - The product code to search for.
 * @param {object} [networkThrottling=null] - Optional network throttling configuration.
 *   When null (default), no throttling is applied.
 *   When provided, should contain: {downloadSpeed, uploadSpeed, latency} in bytes/second and milliseconds.
 * @returns {Promise<Array|null>} Array of scraped products or null if scraping failed.
 */
async function runIntercarsScraper(productCode, networkThrottling = null) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  if (networkThrottling) {
    console.log(`🌐 Network throttling enabled for ${SCRAPER_NAME}`);
  }
  let page;
  let browser;
  try {
    await connect({
      headless: 'auto',
      args: [],
      customConfig: {
        userDataDir: PROFILE_PATH,
      },
      skipTarget: [],
      fingerprint: true,
      turnstile: true,
      connectOption: {}

    })
      .then(async response => {
        browser = response.browser;
        page = response.page;
      })
      .catch(error => {
        console.log(error.message)
      })

    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, COOKIES_PATH);
    await page.setViewport({
      width: 1366,
      height: 768,
    });

    // Apply network throttling if specified
    if (networkThrottling) {
      const { applyNetworkThrottling } = require('../../shared/browser');
      await applyNetworkThrottling(page, networkThrottling);
    }

    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: 15000 });
    await sleep(randomDelay(500, 1000));

    try {
      //await page.waitForNavigation({ waitUntil: 'networkidle2' });
      await page.waitForSelector(LOGGED_IN_SELECTOR, { timeout: 3000 });
    } catch {
      console.log('🔐 Not logged in or session expired. Performing login...');

      const isOnUsernamePage = await page.$(USERNAME_SELECTOR) !== null;
      const isOnPasswordPage = await page.$(PASSWORD_SELECTOR) !== null;

      if (isOnUsernamePage || isOnPasswordPage) {
        console.log(`Navigating to login page: ${LOGIN_URL}`);
        //await page.goto(LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: 6000 });

        if (isOnUsernamePage) {
          await page.waitForSelector(USERNAME_SELECTOR);
          // Type username with human-like delay per character
          for (const char of process.env.INTERCARS_USER) {
            await page.type(USERNAME_SELECTOR, char, { delay: randomDelay(100, 200) });
          }
          await sleep(randomDelay(500, 1000));
          await page.click(NEXT_BUTTON_SELECTOR);
          await sleep(randomDelay(1000, 1500));
        }

        await page.waitForSelector(PASSWORD_SELECTOR);
        await sleep(randomDelay(500, 1000));
        // Type password with human-like delay per character
        for (const char of process.env.INTERCARS_PASSWORD) {
          await page.type(PASSWORD_SELECTOR, char, { delay: randomDelay(100, 200) });
        }
        await sleep(randomDelay(500, 1000));

        const keepSignedInCheckbox = await page.$(KEEP_ME_AUTHENTICATED_SELECTOR);
        if (keepSignedInCheckbox) {
          await keepSignedInCheckbox.click();
          await sleep(randomDelay(500, 1000));
        }

        // Simulate mouse movement to sign-in button before clicking
        const signInButton = await page.$(SIGN_IN_BUTTON_SELECTOR);
        if (signInButton) {
          const box = await signInButton.boundingBox();
          if (box) {
            await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2, { steps: 20 });
            await sleep(randomDelay(300, 600));
          }
          await Promise.all([
            page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 60000 }),
            signInButton.click(),
          ]);
        } else {
          await page.click(SIGN_IN_BUTTON_SELECTOR);
          await Promise.all([
            page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 60000 }),
          ]);
        }
        await saveCookiesToFile(page, COOKIES_PATH);
        saveSessionUrlToFile(page.url(), SESSION_URL_PATH);
      } else {
        console.log('🔄 Already on the login page.');
      }

    }


  // Handle promotional pop-up if it appears before searching for product code
  const promoCloseSelector = 'div.modal__close';
  try {
    await page.waitForSelector(promoCloseSelector, { timeout: 5000 });
    console.log('Promotional pop-up detected, closing it...');
    await page.click(promoCloseSelector);
    await sleep(randomDelay(500, 1000));
  } catch (e) {
    console.log('No promotional pop-up detected.');
  }


    console.log(`🔍 Searching for product code: ${productCode}`);
    await page.waitForSelector(SEARCH_INPUT_SELECTOR);

    await page.type(SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay(100, 200) });
    await sleep(randomDelay(300, 600));
    await page.click(SEARCH_BUTTON_SELECTOR);

    // Wait for navigation after search click
    await page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 60000 });

    // Pagination handling
    const products = [];

    async function scrapeCurrentPage() {
      console.log('Scraping data from current page...');
      // Wait for the product price info to be loaded to ensure page is fully loaded

      // Wait until all price containers have valid price values
      await page.waitForFunction(() => {
        const priceBlocks = document.querySelectorAll('div.productretailprice__content');
        return Array.from(priceBlocks).every(block => {
          const net = block.querySelector('.productpricetoggle__net .quantity__amount')?.textContent.trim();
          const gross = block.querySelector('.productpricetoggle__gross .quantity__amount')?.textContent.trim();
          return net && gross;
        });
      }, { timeout: 15000 });

      // Wait for Marketplace section to load if it exists
      try {
        const marketplaceExists = await page.$('.js-marketplace');
        if (marketplaceExists) {
          console.log('Marketplace section detected, checking if loading...');
          // Wait for marketplace loading to complete (up to 10 seconds)
          await page.waitForFunction(() => {
            const marketplace = document.querySelector('.js-marketplace');
            if (!marketplace) return true; // No marketplace, continue
            const loadingIndicator = marketplace.querySelector('.sc-jYgujM.hEdSqm');
            return !loadingIndicator; // Return true when loading indicator is gone
          }, { timeout: 10000 });
          console.log('Marketplace loading completed');
        }
      } catch (error) {
        console.log('Marketplace loading timeout or error, proceeding anyway:', error.message);
      }

      const products = await page.evaluate(() => {
        const products = [];

        // Extract Marketplace products if they exist
        const marketplaceContainer = document.querySelector('.js-marketplace');
        if (marketplaceContainer) {

          // Check if marketplace has products (not the "no results" variant)
          const noResultsText = marketplaceContainer.querySelector('.sc-hrbTSB.kiZbzX');
          const hasNoResults = noResultsText && noResultsText.textContent.includes('Nu s-au găsit produse');

          if (!hasNoResults) {
            // Extract marketplace products
            const marketplaceProducts = marketplaceContainer.querySelectorAll('a.sc-fEJpot.iPryXf');
            console.log(`Found ${marketplaceProducts.length} marketplace products`);
            marketplaceProducts.forEach(productLink => {
              // Extract name from title attribute or text content
              const nameElement = productLink.querySelector('.sc-goKQVP.cWzvxL');
              const name = nameElement ? (nameElement.getAttribute('title') || nameElement.textContent.trim()) : null;

              // Extract price (use gross price with TVA)
              const priceElement = productLink.querySelector('.productpricetoggle__gross .sc-gwZUyZ.ZaJfG span');
              let retailPrice = null;
              if (priceElement) {
                const priceText = priceElement.textContent.trim();
                const match = priceText.match(/([\d.,]+)/);
                if (match) {
                  const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
                  const numeric = parseFloat(cleanedNumber);
                  if (!isNaN(numeric)) {
                    retailPrice = numeric;
                  }
                }
              }

              if (name) {
                products.push({
                  name,
                  brand: null, // Marketplace products don't have separate brand info
                  productCode: name, // Use name as product code for marketplace products
                  internalCode: null,
                  delivery: null,
                  retailPrice,
                  exchangeValue: null,
                  availability: "INTERCARS_MARKETPLACE", // Indicate this is a marketplace product
                  availabilitySummary: "External Delivery Only", // All marketplace products are external
                  returnable: true, // Default assumption for marketplace products
                  provider: 'INTERCARS',
                });
              }
            });
          }
        }

        // Then, extract regular products and exact match products
        const rows = document.querySelectorAll('table.listingcollapsed__wrapper tbody.listingcollapsed__item');
        rows.forEach(row => {
          // Check if this is an exact match product (alternative structure from intercars.exception)
          const isExactMatch = row.classList.contains('is-exactMatch');

          // Extract name - for exact match products, name might be empty, use product code as fallback
          let name = row.querySelector('div.productname.productname--listingcollapsed')?.textContent.trim() || null;

          // Extract brand - handle both image title and direct text content
          let brand = row.querySelector('div.listingcollapsed__manufacturer img')?.getAttribute('title') ||
                     row.querySelector('div.listingcollapsed__manufacturer')?.textContent.trim() || null;

          const delivery = row.querySelector('div.productdelivery__date.js-clk-delivery-date')?.textContent.trim() || null;
          //const availability = row.querySelector('div.productdelivery__stockinfo')?.textContent.trim() || null;
          const priceNet = row.querySelector('div.productpricetoggle__net .quantity__amount')?.textContent.trim() || null;
          const priceGross = row.querySelector('div.productpricetoggle__gross .quantity__amount')?.textContent.trim() || null;
          const towCode = row.getAttribute('data-product-code') || null;
          let productCode = row.querySelector('a.activenumber--listingcollapsed')?.getAttribute('data-id') || null;

          // For exact match products, if productCode is null, try to get it from the link text
          if (!productCode && isExactMatch) {
            productCode = row.querySelector('a.activenumber--listingcollapsed')?.textContent.trim() || null;
          }

          // If name is empty but we have a product code, use the product code as the name
          if (!name && productCode) {
            name = productCode;
          }

          const nonReturnable = row.querySelector('.productstatus__item--noreturn-label') !== null;

          // 🔍 Extract availability list
          // const availability = [];
          // const availabilityBlocks = row.querySelectorAll('div.productdelivery__stockinfo');

          // availabilityBlocks.forEach(block => {
          //   const source = block.querySelector('[data-clk-listing-item-availability-branch]')?.getAttribute('data-clk-listing-item-availability-branch')?.trim();
          //   const quantityText = block.querySelector('[data-clk-listing-item-availability-amount]')?.getAttribute('data-clk-listing-item-availability-amount')?.trim();
          //   const quantity = quantityText !== undefined ? parseInt(quantityText.replace(/\D/g, ''), 10) || 0 : null;

          //   if (source && quantity > 0) {
          //     availability.push({ source, quantity });
          //   }
          // });
          let availability = '';
          const availabilityBlocks = row.querySelectorAll('div.productdelivery__stockinfo');

          availabilityBlocks.forEach(block => {
            const source = block.querySelector('[data-clk-listing-item-availability-branch]')?.getAttribute('data-clk-listing-item-availability-branch')?.trim();
            const quantityText = block.querySelector('[data-clk-listing-item-availability-amount]')?.getAttribute('data-clk-listing-item-availability-amount')?.trim();
            const quantity = quantityText !== undefined ? parseInt(quantityText.replace(/\D/g, ''), 10) || 0 : null;

            if (source && quantity > 0) {
              availability += `${source}:${quantity}/`;
            }
          });
          availability = availability.replace(/\/$/, ''); // Remove trailing slash

          // Extract exchange value if present
          let exchangeValue = null;
          const oldCoreBlock = row.querySelector('div.productoldcore__texts');
          if (oldCoreBlock) {
            // Get only text nodes (ignore <div>s inside)
            const rawText = Array.from(oldCoreBlock.childNodes)
              .filter(node => node.nodeType === Node.TEXT_NODE)
              .map(node => node.textContent.trim())
              .filter(text => text.length > 0);

            if (rawText.length > 1) {
              const lastValue = rawText[rawText.length - 1];
              const parsed = parseFloat(lastValue.replace(/[^\d,]/g, '').replace(',', '.'));
              if (!isNaN(parsed)) {
                exchangeValue = parsed;
              }
            }
          }

          // Process the priceGross value - multiply by 1.2 and keep only first two digits
          let adjustedPrice = null;
          if (priceGross) {
            // Extract numeric part and handle European number format (e.g., "1.234,56")
            const match = priceGross.match(/([\d.,]+)/);
            if (match) {
              // First, remove all dots (thousands separators)
              // Then, replace comma with dot (decimal separator)
              const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
              const numeric = parseFloat(cleanedNumber);
              if (!isNaN(numeric)) {
                // Multiply by 1.2 (add 20% margin) and keep only two decimal places
                const multiplied = numeric * 1.2;
                // Convert back to number with two decimal places
                adjustedPrice = parseFloat(multiplied.toFixed(2));
              }
            }
          }

          // Process fallback priceGross to ensure it's also a number
          let fallbackPrice = null;
          if (priceGross && !adjustedPrice) {
            const match = priceGross.match(/([\d.,]+)/);
            if (match) {
              const cleanedNumber = match[1].replace(/\./g, '').replace(',', '.');
              const numeric = parseFloat(cleanedNumber);
              if (!isNaN(numeric)) {
                fallbackPrice = numeric;
              }
            }
          }

          // Determine availabilitySummary based on delivery field
          let availabilitySummary = 'Unavailable'; // Default value
          if (delivery) {
            // Define delivery mapping
            const deliveryMapping = {
              "Available": [
                "1 zi lucrătoare",
                "1 zi lucrătoare (în filiala)",
                "2 zile lucrătoare",
                "Astăzi (în filiala)"
              ],
              "External Delivery Only": [
                "3+ zile lucrătoare"
              ],
              "Unavailable": [
                "Întreabă în filială"
              ]
            };

            // Find matching category for the delivery value
            for (const [category, deliveryValues] of Object.entries(deliveryMapping)) {
              if (deliveryValues.includes(delivery)) {
                availabilitySummary = category;
                break;
              }
            }
          }

          // Accept products that have either a name or a product code (or both)
          if (name || productCode) {
            // Log product type for debugging
            if (isExactMatch) {
              console.log(`Found exact match product: ${productCode || name}`);
            }

            products.push({
              name,
              brand,
              productCode,
              internalCode: towCode,
              delivery,
              retailPrice: adjustedPrice || fallbackPrice, // Use adjusted price if available, otherwise use processed fallback
              exchangeValue, // ✅ NEW FIELD
              availability,
              availabilitySummary: availabilitySummary,
              returnable: !nonReturnable,
              provider: 'INTERCARS',
            });
          }
        });
        return products;
      });
      return products;
    }

    // Scrape first page
    let pageProducts = await scrapeCurrentPage();
    products.push(...pageProducts);

    // Check if pagination exists
    const paginationExists = await page.$('div.tableinfo__groupitem.tableinfo__groupitem--separated ul.pagination') !== null;

    if (paginationExists) {
      console.log('Pagination detected, navigating through pages...');
      // Get all page links except the current page and navigation arrows
      const pageLinks = await page.$$eval('div.tableinfo__groupitem.tableinfo__groupitem--separated ul.pagination li.pagination__item a.pagination__page', links =>
        links.map(link => ({ href: link.href, text: link.textContent.trim() }))
      );

      for (const link of pageLinks) {
        console.log(`Navigating to page ${link.text}...`);
        await Promise.all([
          page.goto(link.href, { waitUntil: 'domcontentloaded', timeout: 60000 }),
          page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 60000 }),
        ]);
        pageProducts = await scrapeCurrentPage();
        products.push(...pageProducts);
      }
    } else {
      console.log('No pagination detected, single page of results.');
    }


    const results = products;

    console.log(`📊 Scraped ${results.length} products.`);
    console.log(JSON.stringify(results, null, 2)); // Output results

    // Close browser in success case
    if (browser) {
      try {
        await browser.close();
        console.log('🔒 Browser closed successfully.');
      } catch (closeErr) {
        console.error('Error closing browser:', closeErr);
      }
    }

    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully.`);
    return results;

  } catch (err) {
    console.error(`❌ Error in ${SCRAPER_NAME}:`, err);
    if (browser) {
      try {
        browser.close();
      } catch (closeErr) {
        console.error('Error closing browser:', closeErr);
      }
    }
    return null;
  }
}

module.exports = {
  runIntercarsScraper,
};

