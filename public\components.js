/**
 * shadcn/ui-inspired components for the web scraper interface
 * This file contains reusable UI components following shadcn/ui design patterns
 */

// Utility function for combining classes
function cn(...classes) {
  return classes.filter(Boolean).join(' ');
}

// Button component variants
const buttonVariants = {
  default: "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2",
  destructive: "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-destructive text-destructive-foreground hover:bg-destructive/90 h-10 px-4 py-2",
  outline: "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2",
  secondary: "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground hover:bg-secondary/80 h-10 px-4 py-2",
  ghost: "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2",
  link: "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary underline-offset-4 hover:underline h-10 px-4 py-2"
};

const buttonSizes = {
  default: "h-10 px-4 py-2",
  sm: "h-9 rounded-md px-3",
  lg: "h-11 rounded-md px-8",
  icon: "h-10 w-10"
};

// Create a button element with shadcn/ui styling
function createButton(options = {}) {
  const {
    variant = 'default',
    size = 'default',
    className = '',
    children = '',
    ...props
  } = options;

  const button = document.createElement('button');
  button.className = cn(buttonVariants[variant], buttonSizes[size], className);

  if (typeof children === 'string') {
    button.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    button.appendChild(children);
  }

  // Apply additional properties
  Object.keys(props).forEach(key => {
    if (key !== 'children') {
      button[key] = props[key];
    }
  });

  return button;
}

// Input component
function createInput(options = {}) {
  const {
    type = 'text',
    className = '',
    ...props
  } = options;

  const input = document.createElement('input');
  input.type = type;
  input.className = cn(
    "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
    className
  );

  // Apply additional properties
  Object.keys(props).forEach(key => {
    input[key] = props[key];
  });

  return input;
}

// Card component
function createCard(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const card = document.createElement('div');
  card.className = cn(
    "rounded-lg border border-border bg-card text-card-foreground shadow-sm",
    className
  );

  if (typeof children === 'string') {
    card.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    card.appendChild(children);
  }

  return card;
}

// Card Header
function createCardHeader(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const header = document.createElement('div');
  header.className = cn("flex flex-col space-y-1.5 p-6", className);

  if (typeof children === 'string') {
    header.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    header.appendChild(children);
  }

  return header;
}

// Card Title
function createCardTitle(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const title = document.createElement('h3');
  title.className = cn(
    "text-2xl font-semibold leading-none tracking-tight",
    className
  );

  if (typeof children === 'string') {
    title.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    title.appendChild(children);
  }

  return title;
}

// Card Content
function createCardContent(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const content = document.createElement('div');
  content.className = cn("p-6 pt-0", className);

  if (typeof children === 'string') {
    content.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    content.appendChild(children);
  }

  return content;
}

// Badge component
function createBadge(options = {}) {
  const {
    variant = 'default',
    className = '',
    children = ''
  } = options;

  const badgeVariants = {
    default: "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
    secondary: "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
    destructive: "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
    outline: "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground"
  };

  const badge = document.createElement('div');
  badge.className = cn(badgeVariants[variant], className);

  if (typeof children === 'string') {
    badge.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    badge.appendChild(children);
  }

  return badge;
}

// Select component (simplified)
function createSelect(options = {}) {
  const {
    className = '',
    children = '',
    ...props
  } = options;

  const select = document.createElement('select');
  select.className = cn(
    "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
    className
  );

  if (typeof children === 'string') {
    select.innerHTML = children;
  }

  // Apply additional properties
  Object.keys(props).forEach(key => {
    if (key !== 'children') {
      select[key] = props[key];
    }
  });

  return select;
}

// Table components
function createTable(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const table = document.createElement('table');
  table.className = cn("w-full caption-bottom text-sm", className);

  if (typeof children === 'string') {
    table.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    table.appendChild(children);
  }

  return table;
}

function createTableHeader(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const thead = document.createElement('thead');
  thead.className = cn("[&_tr]:border-b", className);

  if (typeof children === 'string') {
    thead.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    thead.appendChild(children);
  }

  return thead;
}

function createTableBody(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const tbody = document.createElement('tbody');
  tbody.className = cn("[&_tr:last-child]:border-0", className);

  if (typeof children === 'string') {
    tbody.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    tbody.appendChild(children);
  }

  return tbody;
}

function createTableRow(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const tr = document.createElement('tr');
  tr.className = cn(
    "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
    className
  );

  if (typeof children === 'string') {
    tr.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    tr.appendChild(children);
  }

  return tr;
}

function createTableHead(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const th = document.createElement('th');
  th.className = cn(
    "h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",
    className
  );

  if (typeof children === 'string') {
    th.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    th.appendChild(children);
  }

  return th;
}

function createTableCell(options = {}) {
  const {
    className = '',
    children = ''
  } = options;

  const td = document.createElement('td');
  td.className = cn("p-4 align-middle [&:has([role=checkbox])]:pr-0", className);

  if (typeof children === 'string') {
    td.innerHTML = children;
  } else if (children instanceof HTMLElement) {
    td.appendChild(children);
  }

  return td;
}

// Export all components
window.UI = {
  createButton,
  createInput,
  createCard,
  createCardHeader,
  createCardTitle,
  createCardContent,
  createBadge,
  createSelect,
  createTable,
  createTableHeader,
  createTableBody,
  createTableRow,
  createTableHead,
  createTableCell,
  cn
};
