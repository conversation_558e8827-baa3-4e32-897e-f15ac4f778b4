const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const UserPreferencesPlugin = require('puppeteer-extra-plugin-user-preferences');

// Apply the stealth plugin
puppeteer.use(StealthPlugin());

// Apply and configure the user preferences plugin to disable password saving
puppeteer.use(
  UserPreferencesPlugin({
    userPrefs: {
      profile: {
        password_manager_enabled: false,
        default_content_setting_values: {
          automatic_downloads: 1,
        },
      },
      credentials_enable_service: false,
      autofill: {
        enabled: false,
      },
    }
  })
);

/**
 * Launches a Puppeteer browser instance with stealth settings and basic configuration.
 * @param {object} options - Launch options.
 * @param {boolean} [options.headless=true] - Whether to run in headless mode.
 * @param {string} [options.userDataDir] - Path to the user data directory for profile persistence.
 * @param {object} [options.networkThrottling] - Network throttling configuration.
 * @param {number} [options.networkThrottling.downloadSpeed] - Download speed in bytes/second (e.g., 1000000 for 1MB/s).
 * @param {number} [options.networkThrottling.uploadSpeed] - Upload speed in bytes/second.
 * @param {number} [options.networkThrottling.latency] - Latency in milliseconds.
 * @returns {Promise<{browser: import('puppeteer').Browser, page: import('puppeteer').Page}>} - The browser and initial page instance.
 */
async function launchBrowser(options = {}) {
  const { headless = false, userDataDir, networkThrottling } = options;

  const launchOptions = {
    headless: headless,
    defaultViewport: null, // Use default viewport initially
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-password-manager-reauthentication',
      '--disable-save-password-bubble',
      '--disable-autofill-keyboard-accessory-view',
      '--disable-autofill',
      '--disable-credentials-enable-service',
      '--disable-notifications',
      '--window-size=1920,1080',
    ],
    // Ignore default args that might reveal automation
    ignoreDefaultArgs: ['--enable-automation'],
    // Persist profile if userDataDir is provided
    userDataDir: userDataDir,
  };

  const browser = await puppeteer.launch(launchOptions);
  const page = await browser.newPage();

  // Set common browser identity headers
  await page.setViewport({ width: 1366, height: 768 });
  await page.setUserAgent(
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
  );

  // Prevent detection vectors
  await page.evaluateOnNewDocument(() => {
    // Pass webdriver check
    Object.defineProperty(navigator, 'webdriver', {
      get: () => false,
    });
    // Pass Chrome check
    window.chrome = {
      runtime: {},
      // Add other properties if needed
    };
    // Pass permissions check
    const originalQuery = window.navigator.permissions.query;
    window.navigator.permissions.query = (parameters) =>
      parameters.name === 'notifications'
        ? Promise.resolve({ state: Notification.permission })
        : originalQuery(parameters);
    // Pass plugins check
    Object.defineProperty(navigator, 'plugins', {
      get: () => [1, 2, 3, 4, 5], // Mimic typical plugins
    });
    // Pass languages check
     Object.defineProperty(navigator, 'languages', {
      get: () => ['en-US', 'en'],
    });
  });


  // Apply network throttling if specified
  if (networkThrottling) {
    await applyNetworkThrottling(page, networkThrottling);
  }

  console.log(`🚀 Browser launched ${userDataDir ? 'with profile: ' + userDataDir : ''}`);
  return { browser, page };
}

/**
 * Applies network throttling to a Puppeteer page.
 * @param {import('puppeteer').Page} page - The Puppeteer page instance.
 * @param {object} throttlingConfig - Network throttling configuration.
 * @param {number} [throttlingConfig.downloadSpeed=1000000] - Download speed in bytes/second (e.g., 1000000 for 1MB/s).
 * @param {number} [throttlingConfig.uploadSpeed=500000] - Upload speed in bytes/second.
 * @param {number} [throttlingConfig.latency=100] - Latency in milliseconds.
 * @param {number} [throttlingConfig.packetLoss=0] - Packet loss percentage (0-1).
 * @param {number} [throttlingConfig.jitter=0] - Latency jitter in milliseconds.
 * @returns {Promise<void>}
 */
async function applyNetworkThrottling(page, throttlingConfig = {}) {
  const client = await page.target().createCDPSession();

  let {
    downloadSpeed = 1000000, // 1MB/s default
    uploadSpeed = 500000,    // 500KB/s default
    latency = 100,           // 100ms default
    packetLoss = 0,          // 0% packet loss default
    jitter = 0               // 0ms jitter default
  } = throttlingConfig;

  // Enable network throttling
  await client.send('Network.emulateNetworkConditions', {
    offline: false,
    downloadThroughput: downloadSpeed,
    uploadThroughput: uploadSpeed,
    latency: latency
  });

  // Log the applied throttling settings
  let logMessage = `🌐 Applied network throttling: ↓${downloadSpeed/1024}KB/s ↑${uploadSpeed/1024}KB/s, ${latency}ms latency`;

  if (packetLoss > 0) {
    logMessage += `, ${packetLoss * 100}% packet loss`;

    // Note: We're not implementing packet loss simulation through request interception
    // as it can cause issues with the scraper's functionality.
    // Instead, we'll rely on the network throttling to simulate poor conditions.

    // Reduce download/upload speeds further to simulate the effect of packet loss
    const packetLossMultiplier = 1 - packetLoss; // e.g., 40% loss = 0.6 multiplier
    downloadSpeed = Math.floor(downloadSpeed * packetLossMultiplier);
    uploadSpeed = Math.floor(uploadSpeed * packetLossMultiplier);

    // Apply the adjusted throttling
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: downloadSpeed,
      uploadThroughput: uploadSpeed,
      latency: latency
    });

    logMessage += ` (adjusted speeds: ↓${downloadSpeed/1024}KB/s ↑${uploadSpeed/1024}KB/s)`;
  }

  if (jitter > 0) {
    logMessage += `, ${jitter}ms jitter`;

    // For jitter, we'll increase the base latency significantly
    // This simulates the worst-case scenario of jitter
    const totalLatency = latency + Math.floor(jitter * 0.75); // Add 75% of jitter value to base latency

    try {
      await client.send('Network.emulateNetworkConditions', {
        offline: false,
        downloadThroughput: downloadSpeed,
        uploadThroughput: uploadSpeed,
        latency: totalLatency,
      });

      logMessage += ` (effective latency: ${totalLatency}ms)`;
    } catch (e) {
      console.warn('⚠️ Could not apply jitter simulation:', e.message);
    }
  }

  console.log(logMessage);
}

module.exports = {
  launchBrowser,
  applyNetworkThrottling,
};