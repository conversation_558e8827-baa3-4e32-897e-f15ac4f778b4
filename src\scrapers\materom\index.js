/**
 * MATEROM Scraper
 *
 * This scraper handles the Materom website with the following features:
 * - User authentication with session persistence
 * - Lazy loading detection and handling
 * - Smart loading indicator detection (main loader + availability data)
 * - Comprehensive product data extraction including availability information
 * - Debug logging for troubleshooting
 *
 * The scraper follows this flow:
 * 1. Authentication and session management
 * 2. Product search
 * 3. Lazy loading to load all products
 * 4. Wait for main loading indicator to disappear
 * 5. Wait for availability data to load for eligible products
 * 6. Extract and return product data
 */

const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

const { launchBrowser } = require('../../shared/browser');
const {
  saveCookiesToFile,
  loadCookiesFromFile,
  saveSessionUrlToFile,
  loadSessionUrlFromFile,
} = require('../../shared/session');
const { randomDelay, sleep } = require('../../shared/utils');

// ============================================================================
// CONSTANTS
// ============================================================================

const SCRAPER_NAME = 'MATEROM';
const STATE_DIR = path.resolve(__dirname, 'state');
const COOKIES_PATH = path.join(STATE_DIR, 'cookies.json');
const SESSION_URL_PATH = path.join(STATE_DIR, 'session-url.txt');
const PROFILE_PATH = path.join(STATE_DIR, 'profile');

// URLs
const LOGIN_URL = 'https://shop.materom.ro/login';
const DEFAULT_TEST_PAGE = 'https://shop.materom.ro/home';

// Selectors for authentication
const LOGGED_IN_SELECTOR = 'button[id^="headlessui-menu-button"] div:first-child';
const USERNAME_SELECTOR = 'input#username';
const PASSWORD_SELECTOR = 'input#password';
const LOGIN_BUTTON_SELECTOR = 'button[type="submit"].bg-materom-blue';

// Selectors for search and results
const SEARCH_INPUT_SELECTOR = 'input[placeholder*="Caută după cod"]';
const RESULTS_SECTION_SELECTOR = 'div.scrollContainer.space-y-2.py-2';
const RESULT_ITEMS_SELECTOR = 'div.flex-1.min-w-0';

// Lazy loading configuration
const LAZY_LOAD_TIMEOUT = 5000; // Wait 5 seconds for new products to load
const SCROLL_POLL_INTERVAL = 500;
const MAX_NO_NEW_ITEMS_ATTEMPTS = 3; // Stop after 3 attempts with no new items

// Loading detection configuration
const MAIN_LOADER_TIMEOUT = 30000;
const MAIN_LOADER_POLL_INTERVAL = 500;
const AVAILABILITY_TIMEOUT = 25000;
const AVAILABILITY_POLL_INTERVAL = 400;

// Main loading indicator selector (SVG cube icon)
const MAIN_LOADER_SELECTOR = 'svg[data-v-95554c90][viewBox="0 0 20 20"] path[d*="M10.362 1.093a.75.75 0 0 0-.724 0L2.523 5.018"]';

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Handles user authentication for the Materom website
 * @param {Object} page - Puppeteer page object
 */
async function handleAuthentication(page) {
  try {
    await page.waitForSelector(LOGGED_IN_SELECTOR, { timeout: 1000 });
    console.log('✅ Already logged in. Continuing...');
    return;
  } catch {
    console.log('🔐 Not logged in or session expired. Performing login...');
  }

  const isOnLoginPage = await page.$(USERNAME_SELECTOR) !== null;

  if (!isOnLoginPage) {
    console.log(`Navigating to login page: ${LOGIN_URL}`);
    await page.goto(LOGIN_URL, { waitUntil: 'domcontentloaded', timeout: 6000 });
  } else {
    console.log('🔄 Already on the login page.');
  }

  await page.waitForSelector(USERNAME_SELECTOR);
  await page.type(USERNAME_SELECTOR, process.env.MATEROM_USER, { delay: randomDelay() });
  await sleep(randomDelay(300, 600));
  await page.type(PASSWORD_SELECTOR, process.env.MATEROM_PASSWORD, { delay: randomDelay() });
  await sleep(randomDelay(300, 600));
  await page.click(LOGIN_BUTTON_SELECTOR);
  await page.waitForNavigation({ waitUntil: 'networkidle2' });
  await saveCookiesToFile(page, COOKIES_PATH);
  saveSessionUrlToFile(page.url(), SESSION_URL_PATH);
}

/**
 * Performs search for a product code
 * @param {Object} page - Puppeteer page object
 * @param {string} productCode - Product code to search for
 */
async function performSearch(page, productCode) {
  console.log(`🔎 Searching for product code: ${productCode}`);

  await page.waitForSelector(SEARCH_INPUT_SELECTOR, { timeout: 5000 });
  await page.evaluate(sel => document.querySelector(sel).value = '', SEARCH_INPUT_SELECTOR);
  await page.type(SEARCH_INPUT_SELECTOR, productCode, { delay: randomDelay(50, 100) });
  await sleep(randomDelay(300, 600));

  // Ensure the input has focus and press Enter to submit
  await page.focus(SEARCH_INPUT_SELECTOR);
  console.log('⏎ Pressing Enter to submit search…');
  await page.keyboard.press('Enter');

  console.log('🚀 Search submitted. Waiting for results...');
  await sleep(2000);

  try {
    await page.waitForSelector(RESULTS_SECTION_SELECTOR, { timeout: 15000 });
    console.log('📦 Results section loaded.');
  } catch (error) {
    console.log('⚠️ Results section selector failed, trying to debug available elements...');

    // Debug: Check what elements are available
    const availableElements = await page.evaluate(() => {
      const elements = [];
      document.querySelectorAll('div[class*="scroll"], div[class*="container"], div[class*="result"], section').forEach(el => {
        if (el.className) {
          elements.push(`${el.tagName.toLowerCase()}.${el.className.replace(/\s+/g, '.')}`);
        }
      });
      return elements.slice(0, 10);
    });

    console.log('🔍 Available container elements:', availableElements);
    throw error;
  }
}

/**
 * Handles lazy loading by scrolling to load all products
 * @param {Object} page - Puppeteer page object
 */
async function handleLazyLoading(page) {
  console.log('🔄 Handling lazy loading - scrolling to load all products...');

  let currentItemsCount = 0;
  let noNewItemsCount = 0;

  do {
    // Get current count of items
    currentItemsCount = await page.evaluate((sel) => {
      return document.querySelectorAll(sel).length;
    }, RESULT_ITEMS_SELECTOR);

    console.log(`📊 Current product count: ${currentItemsCount}`);

    if (currentItemsCount > 0) {
      // Scroll to the last product item to trigger lazy loading
      await page.evaluate((sel) => {
        const items = document.querySelectorAll(sel);
        if (items.length > 0) {
          const lastItem = items[items.length - 1];
          lastItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, RESULT_ITEMS_SELECTOR);

      console.log('⬇️ Scrolled to last product item, waiting for new products...');
    }

    // Wait and check if new items are loaded
    const waitStart = Date.now();
    let newItemsDetected = false;

    while (Date.now() - waitStart < LAZY_LOAD_TIMEOUT) {
      await sleep(SCROLL_POLL_INTERVAL);

      const newCount = await page.evaluate((sel) => {
        return document.querySelectorAll(sel).length;
      }, RESULT_ITEMS_SELECTOR);

      if (newCount > currentItemsCount) {
        console.log(`✅ New products loaded! Count increased from ${currentItemsCount} to ${newCount}`);
        currentItemsCount = newCount;
        newItemsDetected = true;
        break;
      }
    }

    // Check if we've reached products without prices (end of purchasable products)
    const productsWithoutPrice = await page.evaluate((sel) => {
      const items = document.querySelectorAll(sel);
      let noPriceCount = 0;

      // Check the last few items for price information
      const itemsToCheck = Math.min(5, items.length); // Check last 5 items or all if less than 5

      for (let i = items.length - itemsToCheck; i < items.length; i++) {
        const item = items[i];
        // Look for price container and price elements
        const priceContainer = item.querySelector('div.flex.items-center.px-4.pb-2.gap-x-2');
        const priceElement = item.querySelector('div.text-purple-800, div.text-blue-400');

        // Check if price container exists and has meaningful content
        const hasPrice = priceContainer && priceElement && priceElement.textContent.trim() &&
                        priceElement.textContent.includes('RON');

        if (!hasPrice) {
          noPriceCount++;
        }
      }

      return { noPriceCount, totalChecked: itemsToCheck };
    }, RESULT_ITEMS_SELECTOR);

    if (productsWithoutPrice.noPriceCount >= 3) { // If 3 or more of the last items have no price
      console.log(`🛑 Found ${productsWithoutPrice.noPriceCount}/${productsWithoutPrice.totalChecked} products without price information. Stopping lazy loading.`);
      break;
    }

    if (!newItemsDetected) {
      noNewItemsCount++;
      console.log(`⏳ No new products loaded in ${LAZY_LOAD_TIMEOUT}ms (attempt ${noNewItemsCount}/${MAX_NO_NEW_ITEMS_ATTEMPTS})`);
    } else {
      noNewItemsCount = 0; // Reset counter if new items were found
    }

  } while (noNewItemsCount < MAX_NO_NEW_ITEMS_ATTEMPTS && currentItemsCount > 0);

  console.log(`🏁 Lazy loading complete. Final product count: ${currentItemsCount}`);
}

/**
 * Waits for the main loading indicator to disappear
 * @param {Object} page - Puppeteer page object
 */
async function waitForMainLoader(page) {
  console.log('⏳ Waiting for main loading indicator to disappear...');

  let mainLoaderGone = false;
  const mainLoaderCheckStart = Date.now();

  while (Date.now() - mainLoaderCheckStart < MAIN_LOADER_TIMEOUT) {
    const mainLoaderExists = await page.evaluate((selector) => {
      return document.querySelector(selector) !== null;
    }, MAIN_LOADER_SELECTOR);

    if (!mainLoaderExists) {
      mainLoaderGone = true;
      console.log('✅ Main loading indicator disappeared.');
      break;
    }

    await sleep(MAIN_LOADER_POLL_INTERVAL);
  }

  if (mainLoaderGone) {
    console.log('⏳ Waiting additional 1 second after main loader disappeared...');
    await sleep(1000);
  } else {
    console.warn('⚠️ Main loading indicator did not disappear within timeout. Proceeding anyway...');
  }
}

/**
 * Waits for all eligible products to have availability data loaded
 * @param {Object} page - Puppeteer page object
 */
async function waitForAvailabilityData(page) {
  console.log('⏳ Waiting until all product items have finished loading delivery data...');

  let allDataLoaded = false;
  const spinnerCheckStart = Date.now();

  while (Date.now() - spinnerCheckStart < AVAILABILITY_TIMEOUT) {
    const loadingStatus = await page.evaluate((itemSelector) => {
      const items = Array.from(document.querySelectorAll(itemSelector));
      let spinnersRemaining = 0;
      let itemsWithoutAvailability = 0;
      let debugInfo = [];

      items.forEach((item, index) => {
        // First, check if this product has price information (is eligible)
        const priceContainer = item.querySelector('div.flex.items-center.px-4.pb-2.gap-x-2');
        const priceElement = item.querySelector('div.text-purple-800, div.text-blue-400');
        const hasPrice = priceContainer && priceElement && priceElement.textContent.trim() &&
                        priceElement.textContent.includes('RON');

        // Only check loading status for products with prices (eligible products)
        if (!hasPrice) {
          return; // Skip products without prices
        }

        // Check for loading spinners
        if (item.querySelector('svg.animate-spin')) {
          spinnersRemaining++;
          return;
        }

        // Check if availability information is loaded
        const availabilityContainer = item.querySelector('div.px-2.flex.flex-wrap.items-center.gap-2.justify-between');
        if (availabilityContainer) {
          // Use a more flexible approach - check the text content of the entire container
          const containerText = availabilityContainer.textContent.trim();

          // Check if we have meaningful delivery/stock information in the text
          const hasDeliveryTime = containerText.includes('mâine') || containerText.includes('poimâine') ||
                                 containerText.includes('ZILE') || containerText.includes('zile') ||
                                 containerText.includes('ora') || containerText.includes('INFO TERMEN LIVRARE') ||
                                 containerText.includes('.2025'); // For future delivery dates
          const hasStockInfo = containerText.includes('buc');

          // Also check for availability circles (indicates loaded availability data)
          const hasAvailabilityCircle = item.querySelector('div.h-4.w-4.shrink-0.rounded-full.bg-materom-delivery-main-prime, div.h-4.w-4.shrink-0.rounded-full.bg-48h, div.h-4.w-4.shrink-0.rounded-full.bg-indigo-600');

          // If availability container exists but doesn't have meaningful delivery/stock info or circle, it's still loading
          if (!hasDeliveryTime && !hasStockInfo && !hasAvailabilityCircle) {
            itemsWithoutAvailability++;
            // Get product name for debugging
            const nameEl = item.querySelector('div.text-sm');
            const brandCodeButton = item.querySelector('button.text-lg.whitespace-nowrap.font-bold');
            let productInfo = 'Unknown product';
            if (brandCodeButton) {
              productInfo = brandCodeButton.textContent.trim();
            } else if (nameEl) {
              productInfo = nameEl.textContent.trim();
            }
            debugInfo.push(`Product ${index + 1}: ${productInfo} - Missing availability (container text: "${containerText.substring(0, 50)}...")`);
          }
        } else {
          // For eligible products, if no availability container at all, it might still be loading
          // But we should be more lenient here since some products might not have availability containers
          const hasAnyAvailabilityIndicator = item.textContent.includes('mâine') ||
                                             item.textContent.includes('poimâine') ||
                                             item.textContent.includes('ZILE') ||
                                             item.textContent.includes('zile') ||
                                             item.textContent.includes('INFO TERMEN LIVRARE') ||
                                             item.textContent.includes('.2025') ||
                                             item.textContent.includes('buc') ||
                                             item.querySelector('div.h-4.w-4.shrink-0.rounded-full');
          if (!hasAnyAvailabilityIndicator) {
            itemsWithoutAvailability++;
            // Get product name for debugging
            const nameEl = item.querySelector('div.text-sm');
            const brandCodeButton = item.querySelector('button.text-lg.whitespace-nowrap.font-bold');
            let productInfo = 'Unknown product';
            if (brandCodeButton) {
              productInfo = brandCodeButton.textContent.trim();
            } else if (nameEl) {
              productInfo = nameEl.textContent.trim();
            }
            debugInfo.push(`Product ${index + 1}: ${productInfo} - No availability container`);
          }
        }
      });

      // Count eligible products (those with prices)
      const eligibleProducts = items.filter(item => {
        const priceContainer = item.querySelector('div.flex.items-center.px-4.pb-2.gap-x-2');
        const priceElement = item.querySelector('div.text-purple-800, div.text-blue-400');
        return priceContainer && priceElement && priceElement.textContent.trim() &&
               priceElement.textContent.includes('RON');
      }).length;

      return {
        spinnersRemaining,
        itemsWithoutAvailability,
        totalItems: items.length,
        eligibleProducts,
        debugInfo
      };
    }, RESULT_ITEMS_SELECTOR);

    console.log(`🌀 Still waiting... ${loadingStatus.spinnersRemaining} spinner(s), ${loadingStatus.itemsWithoutAvailability}/${loadingStatus.eligibleProducts} eligible products without availability data.`);

    // Show debug info for products missing availability data
    if (loadingStatus.debugInfo && loadingStatus.debugInfo.length > 0) {
      console.log('🔍 Products missing availability data:');
      loadingStatus.debugInfo.forEach(info => console.log(`   ${info}`));
    }

    if (loadingStatus.spinnersRemaining === 0 && loadingStatus.itemsWithoutAvailability === 0) {
      allDataLoaded = true;
      break;
    }

    await sleep(AVAILABILITY_POLL_INTERVAL);
  }

  if (allDataLoaded) {
    console.log('✅ All eligible product items finished loading delivery/availability data.');
  } else {
    console.warn('⚠️ Some eligible products still had loading indicators or missing availability data. Proceeding anyway...');
  }
}

/**
 * Extracts product data from the page
 * @param {Object} page - Puppeteer page object
 * @returns {Array} Array of product objects
 */
async function extractProductData(page) {
  console.log('📊 Extracting product data...');

  const results = await page.evaluate((itemSelector) => {
    const items = [];
    const productItems = document.querySelectorAll(itemSelector);

    productItems.forEach(item => {
      // Skip items that still have the loading spinner
      if (item.querySelector('svg.animate-spin')) return;

      // Get name from div.text-sm
      const nameEl = item.querySelector('div.text-sm');
      const name = nameEl?.textContent.trim() || 'N/A';

      // Get brand and code from button with spans
      const brandCodeButton = item.querySelector('button.text-lg.whitespace-nowrap.font-bold');
      let brand = 'N/A';
      let code = 'N/A';

      if (brandCodeButton) {
        const spans = brandCodeButton.querySelectorAll('span');
        if (spans.length >= 3) {
          brand = spans[0]?.textContent.trim() || 'N/A';
          code = spans[2]?.textContent.trim() || 'N/A';
        }
      }

      // Initialize price variables
      let retailPrice = null;
      let exchangeValue = null;

      // Extract availability information
      let delivery = null;
      let availability = null;
      let availabilitySummary = null;

      const availabilityContainer = item.querySelector('div.px-2.flex.flex-wrap.items-center.gap-2.justify-between');
      if (availabilityContainer) {

        // Extract information from ALL availability tiles
        // Look for the parent container that holds all availability tiles
        const tilesContainer = availabilityContainer.closest('.group');
        let allAvailabilityTiles = [];

        if (tilesContainer) {
          // Find the space-y-1 container within the group
          const spaceContainer = tilesContainer.querySelector('.space-y-1');
          if (spaceContainer) {
            // Find all individual tile containers (the parent divs of px-2 divs)
            const tileContainers = spaceContainer.children;
            allAvailabilityTiles = Array.from(tileContainers).map(container =>
              container.querySelector('div.px-2.flex.flex-wrap.items-center.gap-2.justify-between')
            ).filter(tile => tile !== null);
          } else {
            // Fallback: if no space-y-1 found, use the current one
            allAvailabilityTiles = [availabilityContainer];
          }
        } else {
          // Fallback: if no group found, use the current one
          allAvailabilityTiles = [availabilityContainer];
        }

        let allTilesInfo = [];
        let firstAvailabilityType = 'unknown';

        allAvailabilityTiles.forEach((availabilityTile, index) => {
          // Find the availability option within this tile
          const availabilityOption = availabilityTile.querySelector('div.flex.items-center');
          if (!availabilityOption) return; // Skip if no availability option found

          // Detect availability type by circle color/class
          const circle = availabilityOption.querySelector('div.h-4.w-4.shrink-0.rounded-full');
          let availabilityType = 'unknown';

          if (circle) {
            if (circle.classList.contains('bg-materom-delivery-main-prime')) {
              availabilityType = 'available'; // Available (quick delivery)
            } else if (circle.classList.contains('bg-48h')) {
              availabilityType = 'indirect'; // Indirect available (longer delivery)
            } else if (circle.classList.contains('bg-indigo-600')) {
              availabilityType = 'external'; // External product (outside country)
            }
          }

          // Store the first tile's type for summary determination
          if (index === 0) {
            firstAvailabilityType = availabilityType;
          }

          // Extract delivery, stock, location, and price information for this tile
          let tileDelivery = null;
          let tileStock = null;
          let tileLocation = null;
          let tilePrice = null;

          // Handle different structures for different availability types
          if (availabilityType === 'external') {
            // For external products, delivery info is in a direct div after the circle
            const deliveryDiv = availabilityOption.querySelector('div.h-4.w-4.shrink-0.rounded-full.bg-indigo-600 + div');
            if (deliveryDiv) {
              const deliveryText = deliveryDiv.textContent.trim();
              if (deliveryText && (deliveryText.includes('ZILE') || deliveryText.includes('zile'))) {
                tileDelivery = deliveryText;
              }
            }

            // Fallback: if delivery not found, search in the entire container
            if (!tileDelivery) {
              const containerText = availabilityOption.textContent;
              const deliveryMatch = containerText.match(/(\d+-?\d*\s*ZILE)/i);
              if (deliveryMatch) {
                tileDelivery = deliveryMatch[1].trim();
              }
            }

            // For external products, stock info is in the "În stoc la furnizor" section
            const stockSpan = availabilityOption.querySelector('span.bg-green-100, span.bg-green-800');
            if (stockSpan) {
              tileStock = stockSpan.textContent.trim();
            }

            // Fallback: if stock not found, search for "În stoc la furnizor" pattern
            if (!tileStock) {
              const containerText = availabilityOption.textContent;
              const stockMatch = containerText.match(/În stoc la furnizor:\s*(\d+\s*buc\.?)/i);
              if (stockMatch) {
                tileStock = stockMatch[1].trim();
              }
            }

            tileLocation = 'External Supplier';
          } else {
            // For available and indirect products, use the original logic
            const deliveryContainer = availabilityOption.querySelector('div.flex.flex-wrap.items-center.gap-x-1');
            if (deliveryContainer) {
              const deliverySpans = deliveryContainer.querySelectorAll('span');
              const deliveryDivs = deliveryContainer.querySelectorAll('div');
              let deliveryParts = [];
              let stockQuantity = null;
              let locationInfo = null;

              // Extract from spans
              deliverySpans.forEach(span => {
                const text = span.textContent.trim();
                if (text) {
                  // Check for stock quantity
                  if (text.includes('buc')) {
                    stockQuantity = text;
                  }
                  // Check for delivery time information
                  else if (text.includes('mâine') || text.includes('poimâine') ||
                          text.includes('ZILE') || text.includes('zile') ||
                          text.includes('ora') || text.includes('.2025') ||
                          text.includes('INFO TERMEN LIVRARE')) {
                    deliveryParts.push(text);
                  }
                }
              });

              // Extract location from divs (after the "/" separator)
              deliveryDivs.forEach(div => {
                const text = div.textContent.trim();
                if (text && text !== '/' && text !== ' / ') {
                  // Check for location information
                  if (text.includes('Suceava') || text.includes('Centru Logistic') ||
                      text.includes('Bacau') || text.includes('București') ||
                      text.includes('Cluj') || text.includes('Timișoara') ||
                      text.includes('Iași') || text.includes('Constanța') ||
                      text.includes('Brașov') || text.includes('Galați') ||
                      text.includes('Craiova') || text.includes('Ploiești')) {
                    locationInfo = text;
                  }
                }
              });

              // Combine delivery information
              if (deliveryParts.length > 0) {
                tileDelivery = deliveryParts.join(' ');
              }

              // Set stock quantity if found
              if (stockQuantity) {
                tileStock = stockQuantity;
              }

              // Set location if found
              if (locationInfo) {
                tileLocation = locationInfo;
              }
            }
          }

          // Extract price for this tile
          const tilePriceSpan = availabilityTile.querySelector('span.text-purple-800, span.text-blue-400');
          if (tilePriceSpan) {
            const priceText = tilePriceSpan.textContent.trim();
            // Extract numeric part (e.g., "1.328,95 RON" -> "1328.95")
            const priceMatch = priceText.match(/([\d.,]+)/);
            if (priceMatch) {
              // Handle Romanian number format (dots as thousand separators, comma as decimal)
              const cleanedPrice = priceMatch[1].replace(/\./g, '').replace(',', '.');
              const numericPrice = parseFloat(cleanedPrice);
              if (!isNaN(numericPrice)) {
                tilePrice = numericPrice;
              } else {
                tilePrice = priceText;
              }
            } else {
              tilePrice = priceText;
            }
          }

          // Build tile information string based on availability type
          let tileInfo = '';
          if (availabilityType === 'external') {
            // For external products: "location/delivery/stock price"
            if (tileLocation) tileInfo += tileLocation;
            tileInfo += '/';
            if (tileDelivery) tileInfo += tileDelivery;
            tileInfo += '/';
            // Combine stock and price with space, not separator
            let stockPriceCombined = '';
            if (tileStock) stockPriceCombined += tileStock;
            if (tilePrice) {
              if (stockPriceCombined) stockPriceCombined += ' ';
              stockPriceCombined += tilePrice;
            }
            tileInfo += stockPriceCombined;
          } else {
            // For within-country products: "location/delivery/stock price"
            if (tileLocation) tileInfo += tileLocation;
            tileInfo += '/';
            if (tileDelivery) tileInfo += tileDelivery;
            tileInfo += '/';
            // Combine stock and price with space, not separator
            let stockPriceCombined = '';
            if (tileStock) stockPriceCombined += tileStock;
            if (tilePrice) {
              if (stockPriceCombined) stockPriceCombined += ' ';
              stockPriceCombined += tilePrice;
            }
            tileInfo += stockPriceCombined;
          }

          // Add to tiles array if we have meaningful information
          if (tileDelivery || tileStock || tileLocation || tilePrice) {
            allTilesInfo.push(tileInfo);
          }

          // For the first tile, set the main delivery and availability fields, and extract prices
          if (index === 0) {
            delivery = tileDelivery;
            availability = tileStock;

            // Extract retailPrice and exchangeValue from the first tile
            // Look for price in span with text-purple-800 or text-blue-400 class
            const priceSpan = availabilityTile.querySelector('span.text-purple-800, span.text-blue-400');
            if (priceSpan) {
              const priceText = priceSpan.textContent.trim();
              // Extract numeric part (e.g., "1.328,95 RON" -> "1328.95")
              const priceMatch = priceText.match(/([\d.,]+)/);
              if (priceMatch) {
                // Handle Romanian number format (dots as thousand separators, comma as decimal)
                const cleanedPrice = priceMatch[1].replace(/\./g, '').replace(',', '.');
                const numericPrice = parseFloat(cleanedPrice);
                if (!isNaN(numericPrice)) {
                  retailPrice = numericPrice;
                } else {
                  retailPrice = priceText;
                }
              } else {
                retailPrice = priceText;
              }
            }

            // Extract exchangeValue from orange background div
            const exchangeDiv = availabilityTile.querySelector('div.bg-orange-500 span.uppercase');
            if (exchangeDiv) {
              const exchangeText = exchangeDiv.textContent.trim();
              // Extract numeric part (e.g., "615,02 RON" -> "615.02")
              const exchangeMatch = exchangeText.match(/([\d.,]+)/);
              if (exchangeMatch) {
                // Handle Romanian number format (dots as thousand separators, comma as decimal)
                const cleanedExchange = exchangeMatch[1].replace(/\./g, '').replace(',', '.');
                const numericExchange = parseFloat(cleanedExchange);
                if (!isNaN(numericExchange)) {
                  exchangeValue = numericExchange;
                } else {
                  exchangeValue = exchangeText;
                }
              } else {
                exchangeValue = exchangeText;
              }
            }
          }
        });

        // Combine all tiles information with '|' separator
        const allTilesString = allTilesInfo.join('|');
        if (allTilesString) {
          availability = allTilesString;
        }

        // Determine availability summary based on first tile type and content
        if (firstAvailabilityType === 'available') {
          availabilitySummary = 'Available';
        } else if (firstAvailabilityType === 'indirect') {
          availabilitySummary = 'Available';
        } else if (firstAvailabilityType === 'external') {
          availabilitySummary = 'External Delivery Only';
        } else if (availability && availability.includes('buc')) {
          availabilitySummary = 'Available';
        } else if (delivery && (delivery.includes('mâine') || delivery.includes('poimâine'))) {
          availabilitySummary = 'Available';
        } else {
          availabilitySummary = 'Unavailable';
        }
      }

      // Set other fields as per new mappings
      const internalCode = null;

      // Set returnable based on availabilitySummary
      const returnable = availabilitySummary !== 'External Delivery Only';

      // Only add items that have a valid price
      if (retailPrice !== null) {
        items.push({
          name,
          brand,
          code,
          internalCode,
          delivery,
          retailPrice,
          exchangeValue,
          availability,
          availabilitySummary,
          returnable,
          provider: 'MATEROM'
        });
      }
    });
    return items;
  }, RESULT_ITEMS_SELECTOR);

  console.log(`📊 Scraped ${results.length} products.`);
  return results;
}

// ============================================================================
// MAIN SCRAPER FUNCTION
// ============================================================================

/**
 * Main function to run the Materom scraper.
 * @param {string} productCode - The product code to search for.
 * @param {object} [networkThrottling=null] - Optional network throttling configuration.
 *   When null (default), no throttling is applied.
 *   When provided, should contain: {downloadSpeed, uploadSpeed, latency} in bytes/second and milliseconds.
 * @returns {Promise<Array|null>} Array of scraped products or null if scraping failed.
 */
async function runMateromScraper(productCode, networkThrottling = null) {
  console.log(`🚀 Starting ${SCRAPER_NAME} scraper for code: ${productCode}`);
  if (networkThrottling) {
    console.log(`🌐 Network throttling enabled for ${SCRAPER_NAME}`);
  }
  let browser;

  try {
    // Initialize browser and page
    const { browser: launchedBrowser, page } = await launchBrowser({
      userDataDir: PROFILE_PATH,
      networkThrottling: networkThrottling
    });
    browser = launchedBrowser;

    // Load session and navigate to test page
    let testPage = loadSessionUrlFromFile(SESSION_URL_PATH) || DEFAULT_TEST_PAGE;
    await loadCookiesFromFile(page, COOKIES_PATH);
    await page.goto(testPage, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await sleep(randomDelay(500, 1000));

    // Handle authentication
    await handleAuthentication(page);

    // Perform search
    await performSearch(page, productCode);

    // Handle lazy loading to load all products
    await handleLazyLoading(page);

    // Wait for main loading indicator to disappear
    await waitForMainLoader(page);

    // Wait for availability data to load for all eligible products
    await waitForAvailabilityData(page);

    // Extract product data
    const results = await extractProductData(page);

    // Output results and finish
    console.log(JSON.stringify(results, null, 2));

    // Close browser in success case
    if (browser) {
      try {
        await browser.close();
        console.log('🔒 Browser closed successfully.');
      } catch (closeErr) {
        console.error('Error closing browser:', closeErr);
      }
    }

    console.log(`✅ ${SCRAPER_NAME} scraper finished successfully.`);
    return results;

  } catch (err) {
    console.error(`❌ Error in ${SCRAPER_NAME}:`, err);
    if (browser) await browser.close();
    return null;
  }
}

// ============================================================================
// MODULE EXPORTS
// ============================================================================

module.exports = {
  runMateromScraper,
};

