/**
 * Test script to verify that all scrapers properly accept network throttling parameters
 * and close browsers correctly after scraping.
 * 
 * This script tests each scraper with a simple network throttling configuration
 * to ensure they all support the test-network structure.
 * 
 * Usage: node test-browser-cleanup.js
 */

// Import all scrapers
const { runAutonetScraper } = require('./src/scrapers/autonet/index');
const { runAutototalScraperModular } = require('./src/scrapers/autototal/index');
const { runIntercarsScraper } = require('./src/scrapers/intercars/index');
const { runAutopartnerScraper } = require('./src/scrapers/autopartner/index');
const { runMateromScraper } = require('./src/scrapers/materom/index');
const { runElitScraper } = require('./src/scrapers/elit/index');
const { runBardiScraper } = require('./src/scrapers/bardi/index');

// Test network configuration (fast connection for quick testing)
const TEST_NETWORK_CONFIG = {
  downloadSpeed: 2 * 1024 * 1024, // 2 MB/s
  uploadSpeed: 1 * 1024 * 1024,   // 1 MB/s
  latency: 50                     // 50ms
};

// Map provider names to their scraper functions
const SCRAPER_FUNCTIONS = {
  'autonet': runAutonetScraper,
  'autototal': runAutototalScraperModular,
  'intercars': runIntercarsScraper,
  'autopartner': runAutopartnerScraper,
  'materom': runMateromScraper,
  'elit': runElitScraper,
  'bardi': runBardiScraper
};

/**
 * Test a single scraper with network throttling
 * @param {string} scraperName - Name of the scraper to test
 * @param {string} productCode - Product code to search for
 * @returns {Promise<object>} - Test results
 */
async function testScraper(scraperName, productCode) {
  console.log(`\n🧪 Testing ${scraperName.toUpperCase()} scraper...`);
  console.log(`📦 Product code: ${productCode}`);
  console.log(`🌐 Network throttling: ↓${TEST_NETWORK_CONFIG.downloadSpeed/1024/1024}MB/s ↑${TEST_NETWORK_CONFIG.uploadSpeed/1024/1024}MB/s ${TEST_NETWORK_CONFIG.latency}ms`);
  
  const startTime = Date.now();
  const scraperFunction = SCRAPER_FUNCTIONS[scraperName.toLowerCase()];
  
  if (!scraperFunction) {
    return {
      scraper: scraperName,
      success: false,
      error: `Unknown scraper: ${scraperName}`,
      duration: 0
    };
  }
  
  try {
    // Test the scraper with network throttling
    const results = await scraperFunction(productCode, TEST_NETWORK_CONFIG);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`✅ ${scraperName.toUpperCase()} test completed in ${duration.toFixed(2)}s`);
    console.log(`📊 Found ${results ? results.length : 0} products`);
    
    return {
      scraper: scraperName,
      success: true,
      duration,
      resultCount: results ? results.length : 0,
      hasResults: results && results.length > 0
    };
    
  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.error(`❌ ${scraperName.toUpperCase()} test failed after ${duration.toFixed(2)}s`);
    console.error(`Error: ${error.message}`);
    
    return {
      scraper: scraperName,
      success: false,
      error: error.message,
      duration
    };
  }
}

/**
 * Run tests for all scrapers
 */
async function runAllTests() {
  const productCode = '**********'; // Test product code
  const results = [];
  
  console.log('🚀 Starting browser cleanup and network throttling tests...');
  console.log(`📦 Testing with product code: ${productCode}`);
  console.log(`🧪 Testing ${Object.keys(SCRAPER_FUNCTIONS).length} scrapers\n`);
  
  // Test each scraper sequentially to avoid resource conflicts
  for (const scraperName of Object.keys(SCRAPER_FUNCTIONS)) {
    const result = await testScraper(scraperName, productCode);
    results.push(result);
    
    // Add delay between tests to ensure cleanup
    if (results.length < Object.keys(SCRAPER_FUNCTIONS).length) {
      console.log('⏳ Waiting 3 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // Generate summary report
  console.log('\n========================================');
  console.log('📊 TEST SUMMARY REPORT');
  console.log('========================================');
  
  const successfulTests = results.filter(r => r.success);
  const failedTests = results.filter(r => !r.success);
  
  console.log(`Total Tests: ${results.length}`);
  console.log(`Successful: ${successfulTests.length}`);
  console.log(`Failed: ${failedTests.length}`);
  console.log(`Success Rate: ${((successfulTests.length / results.length) * 100).toFixed(1)}%\n`);
  
  // Detailed results table
  console.log('Scraper'.padEnd(15) + '| Status'.padEnd(10) + '| Duration'.padEnd(10) + '| Products');
  console.log('-'.repeat(15) + '|' + '-'.repeat(9) + '|' + '-'.repeat(9) + '|' + '-'.repeat(8));
  
  for (const result of results) {
    const status = result.success ? '✅ Pass' : '❌ Fail';
    const duration = result.duration ? `${result.duration.toFixed(1)}s` : 'N/A';
    const products = result.success ? (result.resultCount || 0) : 'N/A';
    
    console.log(
      result.scraper.padEnd(15) + 
      '| ' + status.padEnd(8) + 
      '| ' + duration.padEnd(8) + 
      '| ' + products
    );
  }
  
  // Show failed tests details
  if (failedTests.length > 0) {
    console.log('\n❌ Failed Tests:');
    failedTests.forEach(test => {
      console.log(`   - ${test.scraper}: ${test.error}`);
    });
  }
  
  // Recommendations
  console.log('\n🔧 RECOMMENDATIONS:');
  if (successfulTests.length === results.length) {
    console.log('✅ All scrapers successfully support network throttling and proper browser cleanup!');
    console.log('✅ All scrapers are ready for use with the test-network.js script.');
  } else {
    console.log('⚠️ Some scrapers need attention:');
    console.log('   - Check error messages above for specific issues');
    console.log('   - Ensure all scrapers properly accept networkThrottling parameter');
    console.log('   - Verify browser.close() is called in both success and error cases');
  }
  
  console.log('\n🎯 Next steps:');
  console.log('   - Run: node test-network.js <provider> <product-code>');
  console.log('   - Example: node test-network.js autonet **********');
  
  return results;
}

// Run the tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testScraper,
  runAllTests,
  SCRAPER_FUNCTIONS,
  TEST_NETWORK_CONFIG
};
