<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Scraper Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                    borderRadius: {
                        lg: "var(--radius)",
                        md: "calc(var(--radius) - 2px)",
                        sm: "calc(var(--radius) - 4px)",
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"],
                    },
                }
            }
        }
    </script>
</head>
<body class="bg-background text-foreground min-h-screen flex flex-col">
    <!-- Header - Option 1: Subtle muted background (most popular) -->
    <header class="sticky top-0 z-50 w-full border-b border-border bg-muted/30 backdrop-blur supports-[backdrop-filter]:bg-muted/20">
        <div class="container mx-auto px-4 py-4">
            <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                <!-- Logo and Title -->
                <div class="flex items-center space-x-4">
                    <img id="header-logo" src="/logo_light_mode.png" alt="Company Logo" class="h-10 w-auto object-contain">
                    <div>
                        <h1 class="text-2xl font-bold tracking-tight text-foreground">Multi-Scraper</h1>
                        <p class="text-sm text-muted-foreground">Unified product search across suppliers</p>
                    </div>
                </div>

                <!-- Search Form -->
                <form id="search-form" class="flex-1 max-w-xl">
                    <div class="flex gap-2">
                        <input type="text" id="product-code" name="productCode"
                               placeholder="Enter product code (e.g., JRP675)" required
                               class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                        <button type="submit" id="search-button"
                                class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>
                    </div>
                </form>

                <!-- Dark Mode Toggle -->
                <button
                    id="theme-toggle"
                    class="p-2 rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
                    title="Toggle dark mode"
                >
                    <i id="theme-icon" class="fas fa-moon w-4 h-4"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1">
        <div class="container mx-auto px-6 py-8 max-w-7xl">

        <!-- Welcome Hero Section -->
        <div id="welcome-hero" class="flex flex-col items-center justify-center min-h-[60vh] text-center space-y-8">
            <!-- Hero Illustration -->
            <div class="relative hero-float">
                <!-- Main illustration container -->
                <div class="w-80 h-80 mx-auto relative">
                    <!-- Background circle -->
                    <div class="absolute inset-0 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent rounded-full hero-glow"></div>

                    <!-- Search icon illustration -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="relative">
                            <!-- Large search icon -->
                            <div class="w-32 h-32 rounded-full bg-primary/10 flex items-center justify-center border-2 border-primary/20 hero-glow">
                                <i class="fas fa-search text-6xl text-primary/70"></i>
                            </div>

                            <!-- Floating elements around the search icon -->
                            <div class="absolute -top-4 -right-4 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center animate-bounce animate-bounce-1">
                                <i class="fas fa-cog text-blue-600 text-sm"></i>
                            </div>
                            <div class="absolute -bottom-6 -left-6 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center animate-bounce animate-bounce-2">
                                <i class="fas fa-box text-green-600"></i>
                            </div>
                            <div class="absolute top-8 -left-8 w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center animate-bounce animate-bounce-3">
                                <i class="fas fa-truck text-orange-600 text-xs"></i>
                            </div>
                            <div class="absolute -top-8 left-12 w-7 h-7 bg-purple-100 rounded-full flex items-center justify-center animate-bounce animate-bounce-4">
                                <i class="fas fa-building text-purple-600 text-sm"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Animated rings -->
                    <div class="absolute inset-0 rounded-full border-2 border-primary/20 animate-ping" style="animation-duration: 3s;"></div>
                    <div class="absolute inset-4 rounded-full border border-primary/10 animate-ping" style="animation-duration: 2s; animation-delay: 1s;"></div>
                </div>
            </div>

            <!-- Hero Text -->
            <div class="space-y-4 max-w-2xl">
                <h2 class="text-4xl font-bold tracking-tight text-foreground">
                    Find Products Across All Suppliers
                </h2>
                <p class="text-xl text-muted-foreground leading-relaxed">
                    Enter a product code above to search across multiple automotive suppliers simultaneously.
                    Get instant price comparisons, availability, and delivery information.
                </p>
            </div>



            <!-- Call to action -->
            <div class="mt-8">
                <div class="flex items-center space-x-2 text-muted-foreground">
                    <i class="fas fa-arrow-up text-primary"></i>
                    <span class="text-sm">Start by entering a product code in the search bar above</span>
                </div>
            </div>
        </div>

        <div id="loading" class="hidden space-y-6">
            <div class="flex flex-col items-center space-y-4">
                <div class="animate-spin rounded-full h-16 w-16 border-4 border-muted border-t-primary"></div>
                <p class="text-xl font-medium">Searching across all suppliers...</p>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="rounded-lg border border-border bg-card text-card-foreground shadow-sm">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center">
                            <i class="fas fa-server text-primary mr-3"></i>Scraper Status
                        </h3>
                    </div>
                    <div class="p-6 pt-0">
                        <div id="scrapers-status" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <!-- Scraper status items will be added here dynamically -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="rounded-lg border border-border bg-card text-card-foreground shadow-sm">
                    <div class="flex flex-col space-y-1.5 p-6">
                        <h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center">
                            <i class="fas fa-terminal text-primary mr-3"></i>Progress Log
                        </h3>
                    </div>
                    <div class="p-6 pt-0">
                        <div id="progress-log" class="bg-muted p-4 rounded-md border border-border font-mono text-sm h-48 overflow-y-auto custom-scrollbar"></div>
                    </div>
                </div>
            </div>
        </div>

        <div id="results-container" class="hidden space-y-6">
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                <div class="flex flex-col space-y-1.5 p-6">
                    <h2 class="text-2xl font-semibold leading-none tracking-tight flex items-center">
                        <i class="fas fa-chart-pie text-primary mr-3"></i>Summary Statistics
                    </h2>
                </div>
                <div class="p-6 pt-0 space-y-6">
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        <div class="rounded-lg border bg-primary/5 p-4 text-center">
                            <h3 class="text-sm font-medium text-muted-foreground mb-1">Total Products</h3>
                            <p id="total-products" class="text-2xl font-bold text-primary">0</p>
                        </div>
                        <div class="rounded-lg border bg-green-50 p-4 text-center">
                            <h3 class="text-sm font-medium text-muted-foreground mb-1">Successful Scrapers</h3>
                            <p id="successful-scrapers" class="text-2xl font-bold text-green-600">0</p>
                        </div>
                        <div class="rounded-lg border bg-red-50 p-4 text-center">
                            <h3 class="text-sm font-medium text-muted-foreground mb-1">Failed Scrapers</h3>
                            <p id="failed-scrapers" class="text-2xl font-bold text-red-600">0</p>
                        </div>
                        <div class="rounded-lg border bg-primary/5 p-4 text-center">
                            <h3 class="text-sm font-medium text-muted-foreground mb-1">Unique Brands</h3>
                            <p id="unique-brands" class="text-2xl font-bold text-primary">0</p>
                        </div>
                        <div class="rounded-lg border bg-blue-50 p-4 text-center">
                            <h3 class="text-sm font-medium text-muted-foreground mb-1">Available Products</h3>
                            <p id="available-products" class="text-2xl font-bold text-blue-600">0</p>
                        </div>
                        <div class="rounded-lg border bg-orange-50 p-4 text-center">
                            <h3 class="text-sm font-medium text-muted-foreground mb-1">Unavailable Products</h3>
                            <p id="unavailable-products" class="text-2xl font-bold text-orange-600">0</p>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-3 flex items-center">
                            <i class="fas fa-building text-primary mr-2"></i>Products Per Supplier
                        </h3>
                        <div id="supplier-stats" class="flex flex-wrap gap-2"></div>
                    </div>
                </div>
            </div>

            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                <div class="flex flex-col space-y-1.5 p-6">
                    <h2 class="text-2xl font-semibold leading-none tracking-tight flex items-center">
                        <i class="fas fa-search text-primary mr-3"></i>Search Results
                    </h2>
                </div>
                <div class="p-6 pt-0 space-y-6">
                    <div class="flex flex-col lg:flex-row gap-4">
                        <div class="relative flex-grow">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground">
                                <i class="fas fa-filter"></i>
                            </span>
                            <input type="text" id="filter-input" placeholder="Filter results..."
                                   class="flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                        </div>
                        <select id="product-code-filter"
                                class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 lg:w-auto lg:min-w-[180px]">
                            <option value="all">All Product Codes</option>
                        </select>
                        <select id="supplier-filter"
                                class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 lg:w-auto lg:min-w-[150px]">
                            <option value="all">All Suppliers</option>
                        </select>
                        <select id="brand-filter"
                                class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 lg:w-auto lg:min-w-[130px]">
                            <option value="all">All Brands</option>
                        </select>
                    </div>

                    <!-- Availability Filter Checkboxes -->
                    <div class="flex flex-col space-y-3">
                        <h3 class="text-sm font-medium text-muted-foreground flex items-center">
                            <i class="fas fa-filter mr-2"></i>Filter by Availability
                        </h3>
                        <div class="flex flex-wrap gap-3">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" id="availability-available" value="Available" checked
                                       class="rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2">
                                <span class="text-sm flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-1"></i>Available
                                </span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" id="availability-external" value="External Delivery Only" checked
                                       class="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 focus:ring-2">
                                <span class="text-sm flex items-center">
                                    <i class="fas fa-truck text-yellow-600 mr-1"></i>External Delivery
                                </span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" id="availability-unavailable" value="Unavailable" checked
                                       class="rounded border-gray-300 text-red-600 focus:ring-red-500 focus:ring-2">
                                <span class="text-sm flex items-center">
                                    <i class="fas fa-times-circle text-red-600 mr-1"></i>Unavailable
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="rounded-md border overflow-hidden">
                        <div class="overflow-x-auto">
                            <table id="results-table" class="w-full min-w-[1200px] caption-bottom text-sm shadcn-table">
                                <thead class="[&_tr]:border-b">
                                    <tr class="border-b transition-colors">
                                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-48">Product Code</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-40">Brand</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-32">Supplier</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-32">Price</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-48">Availability</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-48">Delivery</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-32">Status</th>
                                    </tr>
                                </thead>
                                <tbody id="results-body" class="[&_tr:last-child]:border-0">
                                    <!-- Results will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="error-container" class="hidden max-w-4xl mx-auto">
            <div class="rounded-lg border bg-destructive/10 text-destructive-foreground shadow-sm">
                <div class="p-6">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-destructive text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-destructive mb-2">Error</h2>
                            <p id="error-message" class="text-destructive"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="border-t border-border bg-muted/30 py-8 mt-auto">
        <div class="container mx-auto px-4">
            <div class="flex flex-col items-center space-y-4">
                <div class="flex items-center space-x-3">
                    <img id="footer-logo" src="/logo_light_mode.png" alt="Company Logo" class="h-8 w-auto object-contain">
                    <span class="font-semibold text-foreground">Multi-Scraper</span>
                </div>
                <div class="text-center text-sm text-muted-foreground space-y-1">
                    <p>© 2023 Multi-Scraper Dashboard | All rights reserved</p>
                    <p>Unified product search across multiple suppliers</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Action Button for Error Logs -->
    <div id="error-logs-fab" class="fixed bottom-6 right-6 hidden z-50">
        <button id="show-error-logs-btn" class="inline-flex items-center justify-center whitespace-nowrap rounded-full text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-destructive text-destructive-foreground hover:bg-destructive/90 w-16 h-16 shadow-lg pulse-animation">
            <i class="fas fa-exclamation-triangle text-2xl"></i>
        </button>
        <span class="absolute -top-2 -right-2 inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80" id="error-count">0</span>
    </div>

    <!-- Error Logs Modal -->
    <div id="error-logs-modal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
        <div class="rounded-lg border bg-card text-card-foreground shadow-lg max-w-4xl w-full max-h-[80vh] flex flex-col mx-4">
            <div class="flex flex-col space-y-1.5 p-6 border-b">
                <div class="flex items-center justify-between">
                    <h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center">
                        <i class="fas fa-exclamation-triangle text-destructive mr-3"></i>
                        Scraper Error Logs
                    </h3>
                    <button id="close-error-logs-modal" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 overflow-y-auto flex-grow custom-scrollbar">
                <div id="error-logs-container" class="space-y-4">
                    <!-- Error logs will be inserted here dynamically -->
                </div>
            </div>
            <div class="flex items-center p-6 border-t">
                <button id="close-error-logs-btn" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground hover:bg-secondary/80 h-10 px-4 py-2">
                    Close
                </button>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="components.js"></script>
    <script src="script.js"></script>
</body>
</html>
